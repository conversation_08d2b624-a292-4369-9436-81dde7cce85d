<?xml version="1.0" encoding="UTF-8"?>
<RSI>
  <!-- KUKA RSI Configuration for Milling Application -->
  <!-- Real-time position correction for machining error compensation -->
  
  <CONFIG>
    <!-- Communication Settings -->
    <IP_NUMBER>*************</IP_NUMBER>
    <PORT>49152</PORT>
    <SENTYPE>ImFree</SENTYPE>
    <ONLYSEND>FALSE</ONLYSEND>
    <PROTOCOL>UDP</PROTOCOL>
    <CYCLE_TIME>12</CYCLE_TIME>
    <TIMEOUT>100</TIMEOUT>
    
    <!-- Buffer Settings -->
    <BUFFER_SIZE>1024</BUFFER_SIZE>
    <MAX_BUFFER>10</MAX_BUFFER>
    
    <!-- Safety Settings -->
    <MAX_CORRECTION>5.0</MAX_CORRECTION>
    <MAX_VELOCITY>100.0</MAX_VELOCITY>
    <EMERGENCY_STOP>TRUE</EMERGENCY_STOP>
  </CONFIG>

  <SEND>
    <!-- Data sent from KUKA to external controller -->
    <ELEMENTS>
      <!-- Current robot position -->
      <ELEMENT TAG="RobotPos">
        <TYPE>FRAME</TYPE>
        <INDEX>1</INDEX>
      </ELEMENT>
      
      <!-- Current robot velocity -->
      <ELEMENT TAG="RobotVel">
        <TYPE>REAL</TYPE>
        <INDEX>2</INDEX>
      </ELEMENT>
      
      <!-- Current tool position -->
      <ELEMENT TAG="ToolPos">
        <TYPE>FRAME</TYPE>
        <INDEX>3</INDEX>
      </ELEMENT>
      
      <!-- Machining status -->
      <ELEMENT TAG="MachiningStatus">
        <TYPE>INT</TYPE>
        <INDEX>4</INDEX>
      </ELEMENT>
      
      <!-- Force feedback -->
      <ELEMENT TAG="ForceData">
        <TYPE>REAL</TYPE>
        <INDEX>5</INDEX>
      </ELEMENT>
      
      <!-- Timestamp -->
      <ELEMENT TAG="TimeStamp">
        <TYPE>INT</TYPE>
        <INDEX>6</INDEX>
      </ELEMENT>
    </ELEMENTS>
  </SEND>

  <RECEIVE>
    <!-- Data received from external controller -->
    <ELEMENTS>
      <!-- Position correction -->
      <ELEMENT TAG="PosCorrection">
        <TYPE>FRAME</TYPE>
        <INDEX>1</INDEX>
      </ELEMENT>
      
      <!-- Velocity correction -->
      <ELEMENT TAG="VelCorrection">
        <TYPE>REAL</TYPE>
        <INDEX>2</INDEX>
      </ELEMENT>
      
      <!-- Feed rate override -->
      <ELEMENT TAG="FeedOverride">
        <TYPE>REAL</TYPE>
        <INDEX>3</INDEX>
      </ELEMENT>
      
      <!-- Emergency stop command -->
      <ELEMENT TAG="EmergencyStop">
        <TYPE>BOOL</TYPE>
        <INDEX>4</INDEX>
      </ELEMENT>
      
      <!-- Control mode -->
      <ELEMENT TAG="ControlMode">
        <TYPE>INT</TYPE>
        <INDEX>5</INDEX>
      </ELEMENT>
      
      <!-- Quality feedback -->
      <ELEMENT TAG="QualityFlag">
        <TYPE>INT</TYPE>
        <INDEX>6</INDEX>
      </ELEMENT>
    </ELEMENTS>
  </RECEIVE>

  <ENVIRONMENT>
    <!-- Environment variables -->
    <VARIABLE>
      <NAME>$RSI_FRI_PRIO</NAME>
      <VALUE>25</VALUE>
    </VARIABLE>
    
    <VARIABLE>
      <NAME>$RSI_TIMEOUT</NAME>
      <VALUE>100</VALUE>
    </VARIABLE>
    
    <VARIABLE>
      <NAME>$RSI_MAX_CORR</NAME>
      <VALUE>5.0</VALUE>
    </VARIABLE>
  </ENVIRONMENT>

</RSI>
