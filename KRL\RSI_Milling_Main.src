&ACCESS RVP
&REL 1
&PARAM TEMPLATE = C:\KRC\Roboter\Template\vorgabe
&PARAM EDITMASK = *

; KUKA RSI Milling Main Program
; Real-time milling with error compensation
; Author: KUKA RSI System
; Date: 2025-07-23

DEF RSI_Milling_Main()

; Declaration of variables
DECL FRAME HOME_POS, <PERSON><PERSON>OACH_POS, MILLING_START
DECL REAL FEED_RATE, SPINDLE_SPEED
DECL INT MILLING_STATUS, ERROR_CODE
DECL BOOL RSI_ACTIVE, EMERGENCY_FLAG

; RSI related variables
DECL RSI_HANDLE RSI_CONTEXT
DECL FRAME POS_CORRECTION
DECL REAL VEL_CORRECTION, FEED_OVERRIDE

; Initialize system
INTERRUPT DECL 3 WHEN $STOPMESS==TRUE DO IR_STOPMESS()
INTERRUPT ON 3

; Set initial parameters
HOME_POS = {X 500, Y 0, Z 600, A 0, B 0, C 0}
APPROACH_POS = {X 300, Y 200, Z 100, A 0, B 0, C 0}
MILLING_START = {X 300, Y 200, Z 50, A 0, B 0, C 0}
FEED_RATE = 1000.0  ; mm/min
SPINDLE_SPEED = 8000.0  ; rpm

; Initialize RSI
CALL RSI_INIT()

; Check RSI initialization
IF RSI_ACTIVE == TRUE THEN
    ; Move to home position
    PTP HOME_POS VEL=100 ACC=100
    
    ; Start spindle
    CALL SPINDLE_ON(SPINDLE_SPEED)
    
    ; Approach workpiece
    LIN APPROACH_POS VEL=500 ACC=100
    
    ; Start milling with RSI correction
    CALL MILLING_PROCESS()
    
    ; Return to home
    LIN APPROACH_POS VEL=500 ACC=100
    PTP HOME_POS VEL=100 ACC=100
    
    ; Stop spindle
    CALL SPINDLE_OFF()
    
ELSE
    ; RSI initialization failed
    HALT
    MSG_T("RSI initialization failed!")
ENDIF

; Cleanup RSI
CALL RSI_CLEANUP()

INTERRUPT OFF 3

END

; RSI Initialization subroutine
DEF RSI_INIT()
    
    ; Load RSI configuration
    RSI_CONTEXT = RSI_CREATE("RSI_Config/RSI_Milling.xml")
    
    ; Check if RSI context is valid
    IF RSI_CONTEXT <> 0 THEN
        RSI_ACTIVE = TRUE
        MSG_T("RSI initialized successfully")
    ELSE
        RSI_ACTIVE = FALSE
        MSG_T("RSI initialization failed")
    ENDIF
    
END

; Main milling process with RSI correction
DEF MILLING_PROCESS()
    
    DECL FRAME TARGET_POS, CORRECTED_POS
    DECL INT POINT_COUNT, I
    DECL REAL CURRENT_FEED
    
    ; Set milling status
    MILLING_STATUS = 1  ; Active milling
    
    ; Move to milling start position
    LIN MILLING_START VEL=200 ACC=50
    
    ; Start RSI real-time control
    RSI_ON(RSI_CONTEXT)
    
    ; Milling path - example rectangular pocket
    POINT_COUNT = 20
    
    FOR I = 1 TO POINT_COUNT
        
        ; Calculate target position (example path)
        TARGET_POS.X = 300 + (I-1) * 10
        TARGET_POS.Y = 200 + SIN(I * 18) * 50
        TARGET_POS.Z = 50
        TARGET_POS.A = 0
        TARGET_POS.B = 0
        TARGET_POS.C = 0
        
        ; Get RSI correction
        CALL GET_RSI_CORRECTION()
        
        ; Apply correction to target position
        CORRECTED_POS = TARGET_POS
        CORRECTED_POS.X = CORRECTED_POS.X + POS_CORRECTION.X
        CORRECTED_POS.Y = CORRECTED_POS.Y + POS_CORRECTION.Y
        CORRECTED_POS.Z = CORRECTED_POS.Z + POS_CORRECTION.Z
        
        ; Apply feed rate correction
        CURRENT_FEED = FEED_RATE * FEED_OVERRIDE
        
        ; Execute corrected movement
        LIN CORRECTED_POS VEL=CURRENT_FEED ACC=50
        
        ; Check for emergency stop
        IF EMERGENCY_FLAG == TRUE THEN
            HALT
            EXIT
        ENDIF
        
    ENDFOR
    
    ; Stop RSI real-time control
    RSI_OFF(RSI_CONTEXT)
    
    ; Set milling status to complete
    MILLING_STATUS = 0
    
END

; Get RSI correction data
DEF GET_RSI_CORRECTION()
    
    ; Read correction data from RSI
    POS_CORRECTION = RSI_GET_FRAME(RSI_CONTEXT, "PosCorrection")
    VEL_CORRECTION = RSI_GET_REAL(RSI_CONTEXT, "VelCorrection")
    FEED_OVERRIDE = RSI_GET_REAL(RSI_CONTEXT, "FeedOverride")
    EMERGENCY_FLAG = RSI_GET_BOOL(RSI_CONTEXT, "EmergencyStop")
    
    ; Send current robot data to external controller
    RSI_SET_FRAME(RSI_CONTEXT, "RobotPos", $POS_ACT)
    RSI_SET_REAL(RSI_CONTEXT, "RobotVel", $VEL_ACT[1])
    RSI_SET_INT(RSI_CONTEXT, "MachiningStatus", MILLING_STATUS)
    
END

; RSI Cleanup
DEF RSI_CLEANUP()
    
    IF RSI_CONTEXT <> 0 THEN
        RSI_CLOSE(RSI_CONTEXT)
        MSG_T("RSI context closed")
    ENDIF
    
END

; Spindle control functions
DEF SPINDLE_ON(SPEED:IN)
    DECL REAL SPEED
    
    ; Start spindle with specified speed
    $OUT[1] = TRUE  ; Spindle enable
    $ANOUT[1] = SPEED / 10000.0 * 100  ; Speed control (0-100%)
    WAIT SEC 2.0  ; Wait for spindle to reach speed
    
END

DEF SPINDLE_OFF()
    
    ; Stop spindle
    $OUT[1] = FALSE
    $ANOUT[1] = 0
    WAIT SEC 1.0
    
END

; Emergency stop interrupt routine
DEF IR_STOPMESS()
    
    EMERGENCY_FLAG = TRUE
    CALL SPINDLE_OFF()
    
    IF RSI_CONTEXT <> 0 THEN
        RSI_OFF(RSI_CONTEXT)
    ENDIF
    
END
