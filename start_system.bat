@echo off
REM KUKA RSI Milling System Startup Script
REM 启动脚本 - Windows版本

echo ========================================
echo KUKA RSI Milling System Startup
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7+ and try again
    pause
    exit /b 1
)

REM Check if numpy is installed
python -c "import numpy" >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing Python dependencies...
    pip install numpy
    if %errorlevel% neq 0 (
        echo Error: Failed to install numpy
        pause
        exit /b 1
    )
)

REM Create logs directory if it doesn't exist
if not exist "logs" mkdir logs

REM Check network connectivity
echo Checking network connectivity...
ping -n 1 ************* >nul 2>&1
if %errorlevel% neq 0 (
    echo Warning: Cannot reach robot at *************
    echo Please check network configuration
    echo.
)

REM Display menu
:menu
echo Select startup option:
echo 1. Start Python RSI Controller
echo 2. Build and start C++ RSI Controller
echo 3. Check system status
echo 4. View configuration
echo 5. Exit
echo.
set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto start_python
if "%choice%"=="2" goto start_cpp
if "%choice%"=="3" goto check_status
if "%choice%"=="4" goto view_config
if "%choice%"=="5" goto exit
echo Invalid choice. Please try again.
goto menu

:start_python
echo.
echo Starting Python RSI Controller...
cd External_Control
python RSI_Controller.py
cd ..
goto menu

:start_cpp
echo.
echo Building C++ RSI Controller...
cd External_Control
if exist "Makefile" (
    make clean
    make
    if %errorlevel% equ 0 (
        echo.
        echo Starting C++ RSI Controller...
        RSI_Controller.exe
    ) else (
        echo Build failed. Please check compiler installation.
    )
) else (
    echo Makefile not found. Trying direct compilation...
    g++ -std=c++17 -o RSI_Controller.exe RSI_Controller.cpp -lws2_32
    if %errorlevel% equ 0 (
        echo.
        echo Starting C++ RSI Controller...
        RSI_Controller.exe
    ) else (
        echo Compilation failed. Please install MinGW or Visual Studio.
    )
)
cd ..
goto menu

:check_status
echo.
echo System Status Check:
echo ==================
echo.
echo Network Configuration:
ipconfig | findstr "IPv4"
echo.
echo Python Version:
python --version
echo.
echo Python Packages:
pip list | findstr numpy
echo.
echo File Structure:
dir /b KRL RSI_Config External_Control Documentation 2>nul
echo.
pause
goto menu

:view_config
echo.
echo Current Configuration:
echo ====================
if exist "config.ini" (
    type config.ini
) else (
    echo config.ini not found
)
echo.
pause
goto menu

:exit
echo.
echo Thank you for using KUKA RSI Milling System
echo System shutdown complete.
pause
exit /b 0
