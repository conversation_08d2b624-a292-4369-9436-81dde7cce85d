#!/usr/bin/env python3
"""
KUKA RSI External Controller for Milling Error Compensation
Real-time position and velocity correction based on sensor feedback

Author: KUKA RSI System
Date: 2025-07-23
"""

import socket
import struct
import time
import threading
import numpy as np
import xml.etree.ElementTree as ET
from dataclasses import dataclass
from typing import Tuple, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class RobotState:
    """Robot state data structure"""
    position: np.ndarray  # [x, y, z, a, b, c]
    velocity: float
    tool_position: np.ndarray
    machining_status: int
    force_data: float
    timestamp: int

@dataclass
class CorrectionData:
    """Correction data structure"""
    position_correction: np.ndarray  # [x, y, z, a, b, c]
    velocity_correction: float
    feed_override: float
    emergency_stop: bool
    control_mode: int
    quality_flag: int

class RSIController:
    """KUKA RSI External Controller"""
    
    def __init__(self, robot_ip: str = "*************", robot_port: int = 49152):
        self.robot_ip = robot_ip
        self.robot_port = robot_port
        self.socket = None
        self.running = False
        
        # Control parameters
        self.max_position_correction = 5.0  # mm
        self.max_velocity_correction = 50.0  # %
        self.cycle_time = 0.012  # 12ms cycle time
        
        # State variables
        self.robot_state = RobotState(
            position=np.zeros(6),
            velocity=0.0,
            tool_position=np.zeros(6),
            machining_status=0,
            force_data=0.0,
            timestamp=0
        )
        
        self.correction_data = CorrectionData(
            position_correction=np.zeros(6),
            velocity_correction=0.0,
            feed_override=1.0,
            emergency_stop=False,
            control_mode=1,
            quality_flag=0
        )
        
        # Error compensation algorithms
        self.error_history = []
        self.force_threshold = 50.0  # N
        self.position_tolerance = 0.1  # mm
        
    def connect(self) -> bool:
        """Establish UDP connection with KUKA robot"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.settimeout(0.1)  # 100ms timeout
            logger.info(f"Connected to KUKA robot at {self.robot_ip}:{self.robot_port}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect: {e}")
            return False
    
    def disconnect(self):
        """Close connection"""
        if self.socket:
            self.socket.close()
            self.socket = None
        logger.info("Disconnected from KUKA robot")
    
    def parse_robot_data(self, data: bytes) -> bool:
        """Parse incoming robot data"""
        try:
            # Parse XML data from robot
            xml_str = data.decode('utf-8')
            root = ET.fromstring(xml_str)
            
            # Extract robot position
            pos_elem = root.find('.//RobotPos')
            if pos_elem is not None:
                self.robot_state.position = np.array([
                    float(pos_elem.get('X', 0)),
                    float(pos_elem.get('Y', 0)),
                    float(pos_elem.get('Z', 0)),
                    float(pos_elem.get('A', 0)),
                    float(pos_elem.get('B', 0)),
                    float(pos_elem.get('C', 0))
                ])
            
            # Extract velocity
            vel_elem = root.find('.//RobotVel')
            if vel_elem is not None:
                self.robot_state.velocity = float(vel_elem.text or 0)
            
            # Extract force data
            force_elem = root.find('.//ForceData')
            if force_elem is not None:
                self.robot_state.force_data = float(force_elem.text or 0)
            
            # Extract machining status
            status_elem = root.find('.//MachiningStatus')
            if status_elem is not None:
                self.robot_state.machining_status = int(status_elem.text or 0)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to parse robot data: {e}")
            return False
    
    def calculate_error_compensation(self) -> CorrectionData:
        """Calculate error compensation based on current robot state"""
        
        # Initialize correction
        correction = CorrectionData(
            position_correction=np.zeros(6),
            velocity_correction=0.0,
            feed_override=1.0,
            emergency_stop=False,
            control_mode=1,
            quality_flag=0
        )
        
        # Force-based compensation
        if abs(self.robot_state.force_data) > self.force_threshold:
            # High force detected - reduce feed rate and apply position correction
            force_error = self.robot_state.force_data - self.force_threshold
            
            # Position correction (move away from high force)
            correction.position_correction[2] = -0.1 * np.sign(force_error)  # Z-axis correction
            
            # Feed rate reduction
            correction.feed_override = max(0.5, 1.0 - abs(force_error) / 100.0)
            
            # Set quality warning
            correction.quality_flag = 1
            
            logger.warning(f"High force detected: {self.robot_state.force_data}N")
        
        # Velocity-based compensation
        if self.robot_state.velocity > 0:
            # Adaptive velocity control based on machining conditions
            velocity_factor = min(1.0, 100.0 / self.robot_state.velocity)
            correction.velocity_correction = (velocity_factor - 1.0) * 10.0
        
        # Position tracking error compensation
        self.error_history.append(self.robot_state.position.copy())
        if len(self.error_history) > 10:
            self.error_history.pop(0)
            
            # Calculate position trend
            if len(self.error_history) >= 3:
                trend = np.mean(np.diff(self.error_history, axis=0), axis=0)
                
                # Predictive correction
                correction.position_correction[:3] += -0.1 * trend[:3]
        
        # Apply limits
        correction.position_correction = np.clip(
            correction.position_correction,
            -self.max_position_correction,
            self.max_position_correction
        )
        
        correction.velocity_correction = np.clip(
            correction.velocity_correction,
            -self.max_velocity_correction,
            self.max_velocity_correction
        )
        
        correction.feed_override = np.clip(correction.feed_override, 0.1, 2.0)
        
        return correction
    
    def create_correction_xml(self, correction: CorrectionData) -> str:
        """Create XML message with correction data"""
        
        xml_data = f"""<?xml version="1.0" encoding="UTF-8"?>
<RSI>
    <PosCorrection X="{correction.position_correction[0]:.3f}" 
                   Y="{correction.position_correction[1]:.3f}" 
                   Z="{correction.position_correction[2]:.3f}" 
                   A="{correction.position_correction[3]:.3f}" 
                   B="{correction.position_correction[4]:.3f}" 
                   C="{correction.position_correction[5]:.3f}" />
    <VelCorrection>{correction.velocity_correction:.3f}</VelCorrection>
    <FeedOverride>{correction.feed_override:.3f}</FeedOverride>
    <EmergencyStop>{str(correction.emergency_stop).lower()}</EmergencyStop>
    <ControlMode>{correction.control_mode}</ControlMode>
    <QualityFlag>{correction.quality_flag}</QualityFlag>
</RSI>"""
        
        return xml_data
    
    def run_control_loop(self):
        """Main control loop"""
        logger.info("Starting RSI control loop")
        self.running = True
        
        while self.running:
            try:
                # Receive data from robot
                data, addr = self.socket.recvfrom(1024)
                
                # Parse robot state
                if self.parse_robot_data(data):
                    
                    # Calculate error compensation
                    correction = self.calculate_error_compensation()
                    
                    # Create response XML
                    response_xml = self.create_correction_xml(correction)
                    
                    # Send correction back to robot
                    self.socket.sendto(response_xml.encode('utf-8'), addr)
                    
                    # Log status
                    if correction.quality_flag > 0:
                        logger.info(f"Correction applied: Pos={correction.position_correction[:3]}, "
                                  f"Feed={correction.feed_override:.2f}")
                
                # Maintain cycle time
                time.sleep(self.cycle_time)
                
            except socket.timeout:
                # No data received - continue
                continue
            except Exception as e:
                logger.error(f"Control loop error: {e}")
                break
        
        logger.info("RSI control loop stopped")
    
    def start(self):
        """Start the RSI controller"""
        if not self.connect():
            return False
        
        # Start control loop in separate thread
        self.control_thread = threading.Thread(target=self.run_control_loop)
        self.control_thread.daemon = True
        self.control_thread.start()
        
        return True
    
    def stop(self):
        """Stop the RSI controller"""
        self.running = False
        if hasattr(self, 'control_thread'):
            self.control_thread.join(timeout=1.0)
        self.disconnect()

def main():
    """Main function"""
    controller = RSIController()
    
    try:
        if controller.start():
            logger.info("RSI Controller started successfully")
            
            # Keep running until interrupted
            while True:
                time.sleep(1.0)
                
    except KeyboardInterrupt:
        logger.info("Shutting down RSI Controller")
    finally:
        controller.stop()

if __name__ == "__main__":
    main()

# Requirements for this program:
# pip install numpy
