# KUKA RSI 铣削系统安装配置指南

## 系统要求

### KUKA机器人端
- KUKA机器人控制器 KRC4 或更新版本
- KUKA系统软件 KSS 8.3 或更新版本
- RSI选件包 (Robot Sensor Interface)
- 以太网接口

### PC控制端
- Windows 10/11 或 Linux Ubuntu 18.04+
- Python 3.7+ (Python版本) 或 GCC 7+ (C++版本)
- 以太网接口
- 最小4GB RAM，推荐8GB+

### 网络要求
- 千兆以太网连接
- 低延迟网络环境 (<1ms)
- 专用网络段（避免网络拥塞）

## 安装步骤

### 第一步：KUKA机器人端配置

#### 1.1 检查RSI选件
```krl
; 在KUKA示教器上检查RSI选件是否已安装
; 路径：配置 > 软件 > 已安装的选件
; 确认 "Robot Sensor Interface" 已安装
```

#### 1.2 复制KRL程序
1. 将以下文件复制到机器人控制器的 `KRC:\R1\Program` 目录：
   - `RSI_Milling_Main.src`
   - `RSI_DataHandler.src`

2. 在KUKA示教器上编译程序：
   ```
   程序 > 选择 > RSI_Milling_Main
   编辑 > 编译
   ```

#### 1.3 配置RSI文件
1. 将 `RSI_Milling.xml` 复制到 `KRC:\ROBOTER\Config\User\Common\SensorInterface\`

2. 修改配置文件中的IP地址（如需要）：
   ```xml
   <IP_NUMBER>*************</IP_NUMBER>  <!-- 机器人IP -->
   ```

#### 1.4 网络配置
1. 在KUKA示教器上配置网络：
   ```
   启动 > 网络配置 > 高级
   ```

2. 设置网络参数：
   - IP地址：*************
   - 子网掩码：*************
   - 网关：***********

3. 重启网络服务或重启控制器

### 第二步：PC端安装

#### 2.1 Python版本安装

1. **安装Python依赖**：
   ```bash
   pip install numpy
   ```

2. **配置网络**：
   - IP地址：***********01
   - 子网掩码：*************
   - 网关：***********

3. **测试连接**：
   ```bash
   ping *************
   ```

4. **运行控制程序**：
   ```bash
   cd External_Control
   python RSI_Controller.py
   ```

#### 2.2 C++版本安装

1. **Windows环境**：
   ```cmd
   # 使用Visual Studio或MinGW编译
   g++ -std=c++17 -o RSI_Controller.exe RSI_Controller.cpp -lws2_32
   ```

2. **Linux环境**：
   ```bash
   g++ -std=c++17 -o RSI_Controller RSI_Controller.cpp -lpthread
   ```

3. **运行程序**：
   ```bash
   ./RSI_Controller
   ```

### 第三步：系统集成测试

#### 3.1 连接测试
1. 启动PC端控制程序
2. 在KUKA示教器上运行RSI_Milling_Main程序
3. 观察连接状态：
   ```
   PC端应显示：Connected to KUKA robot at *************:49152
   KUKA端应显示：RSI initialized successfully
   ```

#### 3.2 数据通讯测试
1. 检查数据传输：
   - 机器人位置数据发送
   - PC端补偿数据接收
   - 双向通讯正常

2. 验证补偿功能：
   - 手动移动机器人
   - 观察PC端数据变化
   - 确认补偿计算正常

#### 3.3 安全功能测试
1. 测试紧急停止：
   ```krl
   ; 在KRL程序中触发紧急停止
   $STOPMESS = TRUE
   ```

2. 测试超时保护：
   - 断开网络连接
   - 观察机器人响应
   - 确认安全停机

## 参数配置

### RSI配置参数

#### 通讯参数
```xml
<CYCLE_TIME>12</CYCLE_TIME>        <!-- 控制周期(ms) -->
<TIMEOUT>100</TIMEOUT>             <!-- 超时时间(ms) -->
<MAX_CORRECTION>5.0</MAX_CORRECTION> <!-- 最大补偿(mm) -->
```

#### 安全参数
```xml
<MAX_VELOCITY>100.0</MAX_VELOCITY>  <!-- 最大速度(mm/s) -->
<EMERGENCY_STOP>TRUE</EMERGENCY_STOP> <!-- 紧急停止使能 -->
```

### 控制器参数

#### Python版本参数
```python
# 在RSI_Controller.py中修改
self.max_position_correction = 5.0  # 最大位置补偿(mm)
self.max_velocity_correction = 50.0 # 最大速度补偿(%)
self.force_threshold = 50.0         # 力阈值(N)
```

#### C++版本参数
```cpp
// 在RSI_Controller.cpp中修改
double max_position_correction = 5.0;  // 最大位置补偿(mm)
double max_velocity_correction = 50.0; // 最大速度补偿(%)
double force_threshold = 50.0;         // 力阈值(N)
```

## 故障排除

### 网络连接问题
1. **无法连接机器人**：
   ```bash
   # 检查网络连通性
   ping *************
   
   # 检查端口是否开放
   telnet ************* 49152
   ```

2. **数据传输异常**：
   - 检查防火墙设置
   - 验证网络延迟
   - 确认RSI配置正确

### RSI初始化失败
1. **检查RSI选件**：
   ```krl
   ; 在KRL程序中检查
   IF $RSI_AVAILABLE == FALSE THEN
       MSG_T("RSI option not available")
   ENDIF
   ```

2. **检查配置文件**：
   - 验证XML文件路径
   - 检查XML语法正确性
   - 确认文件权限

### 性能优化
1. **减少网络延迟**：
   - 使用专用网络
   - 优化网络设备配置
   - 减少网络负载

2. **优化控制算法**：
   - 调整滤波参数
   - 优化补偿算法
   - 减少计算复杂度

## 维护建议

### 定期检查项目
- [ ] 网络连接状态
- [ ] RSI通讯质量
- [ ] 补偿参数有效性
- [ ] 安全功能正常性
- [ ] 系统日志记录

### 备份重要文件
- [ ] KRL程序文件
- [ ] RSI配置文件
- [ ] 控制器参数设置
- [ ] 网络配置信息

### 更新维护
- [ ] 定期更新系统软件
- [ ] 检查硬件状态
- [ ] 优化控制参数
- [ ] 记录维护日志

## 技术支持

如遇到安装或配置问题，请：
1. 检查本指南的故障排除部分
2. 查看系统日志文件
3. 联系技术支持团队

**联系信息**：
- 技术支持：KUKA RSI系统团队
- 文档版本：v1.0
- 更新日期：2025-07-23
