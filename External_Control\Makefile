# Makefile for KUKA RSI Controller
# Supports both Windows and Linux compilation

# Compiler settings
CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -O2

# Target executable name
TARGET = RSI_Controller

# Source files
SOURCES = RSI_Controller.cpp

# Object files
OBJECTS = $(SOURCES:.cpp=.o)

# Platform detection
ifeq ($(OS),Windows_NT)
    # Windows specific settings
    LDFLAGS = -lws2_32
    TARGET_EXT = .exe
    RM = del /Q
    MKDIR = mkdir
else
    # Linux/Unix specific settings
    LDFLAGS = -lpthread
    TARGET_EXT = 
    RM = rm -f
    MKDIR = mkdir -p
endif

# Default target
all: $(TARGET)$(TARGET_EXT)

# Build target
$(TARGET)$(TARGET_EXT): $(OBJECTS)
	$(CXX) $(OBJECTS) -o $@ $(LDFLAGS)
	@echo "Build completed: $(TARGET)$(TARGET_EXT)"

# Compile source files
%.o: %.cpp
	$(CXX) $(CXXFLAGS) -c $< -o $@

# Clean build files
clean:
	$(RM) $(OBJECTS) $(TARGET)$(TARGET_EXT)
	@echo "Clean completed"

# Install Python dependencies
install-python:
	pip install numpy
	@echo "Python dependencies installed"

# Run Python version
run-python:
	python RSI_Controller.py

# Run C++ version
run-cpp: $(TARGET)$(TARGET_EXT)
	./$(TARGET)$(TARGET_EXT)

# Help target
help:
	@echo "Available targets:"
	@echo "  all           - Build C++ version"
	@echo "  clean         - Clean build files"
	@echo "  install-python- Install Python dependencies"
	@echo "  run-python    - Run Python version"
	@echo "  run-cpp       - Build and run C++ version"
	@echo "  help          - Show this help"

.PHONY: all clean install-python run-python run-cpp help
