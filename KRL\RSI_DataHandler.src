&ACCESS RVP
&REL 1
&PARAM TEMPLATE = C:\KRC\Roboter\Template\vorgabe
&PARAM EDITMASK = *

; KUKA RSI Data Handler
; Real-time data processing for milling error compensation
; Author: KUKA RSI System
; Date: 2025-07-23

DEF RSI_DataHandler()

; Global variables for RSI data handling
GLOBAL FRAME G_POS_CORRECTION, G_TOOL_OFFSET
GLOBAL REAL G_VEL_CORRECTION, G_FEED_OVERRIDE
GLOBAL INT G_CONTROL_MODE, G_QUALITY_FLAG
GLOBAL BOOL G_EMERGENCY_STOP, G_RSI_CONNECTED

; Data validation limits
GLOBAL REAL G_MAX_POS_CORR = 5.0  ; Maximum position correction in mm
GLOBAL REAL G_MAX_VEL_CORR = 50.0  ; Maximum velocity correction in %
GLOBAL REAL G_MIN_FEED_OVERRIDE = 0.1  ; Minimum feed override (10%)
GLOBAL REAL G_MAX_FEED_OVERRIDE = 2.0   ; Maximum feed override (200%)

; Initialize data handler
CALL INIT_DATA_HANDLER()

END

; Initialize RSI data handler
DEF INIT_DATA_HANDLER()
    
    ; Initialize correction values to zero
    G_POS_CORRECTION = {X 0, Y 0, Z 0, A 0, B 0, C 0}
    G_TOOL_OFFSET = {X 0, Y 0, Z 0, A 0, B 0, C 0}
    G_VEL_CORRECTION = 0.0
    G_FEED_OVERRIDE = 1.0
    
    ; Initialize control flags
    G_CONTROL_MODE = 1  ; 1=Position control, 2=Force control, 3=Hybrid
    G_QUALITY_FLAG = 0  ; 0=Good, 1=Warning, 2=Error
    G_EMERGENCY_STOP = FALSE
    G_RSI_CONNECTED = FALSE
    
    MSG_T("RSI Data Handler initialized")
    
END

; Process incoming RSI data
DEF PROCESS_RSI_INPUT(RSI_HANDLE:IN)
    DECL RSI_HANDLE RSI_HANDLE
    
    DECL FRAME RAW_POS_CORR
    DECL REAL RAW_VEL_CORR, RAW_FEED_OVERRIDE
    DECL INT RAW_CONTROL_MODE, RAW_QUALITY_FLAG
    DECL BOOL RAW_EMERGENCY_STOP
    
    ; Read raw data from RSI
    RAW_POS_CORR = RSI_GET_FRAME(RSI_HANDLE, "PosCorrection")
    RAW_VEL_CORR = RSI_GET_REAL(RSI_HANDLE, "VelCorrection")
    RAW_FEED_OVERRIDE = RSI_GET_REAL(RSI_HANDLE, "FeedOverride")
    RAW_EMERGENCY_STOP = RSI_GET_BOOL(RSI_HANDLE, "EmergencyStop")
    RAW_CONTROL_MODE = RSI_GET_INT(RSI_HANDLE, "ControlMode")
    RAW_QUALITY_FLAG = RSI_GET_INT(RSI_HANDLE, "QualityFlag")
    
    ; Validate and filter data
    CALL VALIDATE_POSITION_CORRECTION(RAW_POS_CORR)
    CALL VALIDATE_VELOCITY_CORRECTION(RAW_VEL_CORR)
    CALL VALIDATE_FEED_OVERRIDE(RAW_FEED_OVERRIDE)
    CALL VALIDATE_CONTROL_FLAGS(RAW_EMERGENCY_STOP, RAW_CONTROL_MODE, RAW_QUALITY_FLAG)
    
    ; Apply low-pass filter to reduce noise
    CALL APPLY_POSITION_FILTER()
    CALL APPLY_VELOCITY_FILTER()
    
END

; Validate position correction data
DEF VALIDATE_POSITION_CORRECTION(POS_CORR:IN)
    DECL FRAME POS_CORR
    
    ; Check X correction
    IF ABS(POS_CORR.X) > G_MAX_POS_CORR THEN
        POS_CORR.X = SIGN(POS_CORR.X) * G_MAX_POS_CORR
    ENDIF
    
    ; Check Y correction
    IF ABS(POS_CORR.Y) > G_MAX_POS_CORR THEN
        POS_CORR.Y = SIGN(POS_CORR.Y) * G_MAX_POS_CORR
    ENDIF
    
    ; Check Z correction
    IF ABS(POS_CORR.Z) > G_MAX_POS_CORR THEN
        POS_CORR.Z = SIGN(POS_CORR.Z) * G_MAX_POS_CORR
    ENDIF
    
    ; Update global correction
    G_POS_CORRECTION = POS_CORR
    
END

; Validate velocity correction data
DEF VALIDATE_VELOCITY_CORRECTION(VEL_CORR:IN)
    DECL REAL VEL_CORR
    
    ; Limit velocity correction
    IF ABS(VEL_CORR) > G_MAX_VEL_CORR THEN
        VEL_CORR = SIGN(VEL_CORR) * G_MAX_VEL_CORR
    ENDIF
    
    G_VEL_CORRECTION = VEL_CORR
    
END

; Validate feed override data
DEF VALIDATE_FEED_OVERRIDE(FEED_OVERRIDE:IN)
    DECL REAL FEED_OVERRIDE
    
    ; Limit feed override range
    IF FEED_OVERRIDE < G_MIN_FEED_OVERRIDE THEN
        FEED_OVERRIDE = G_MIN_FEED_OVERRIDE
    ENDIF
    
    IF FEED_OVERRIDE > G_MAX_FEED_OVERRIDE THEN
        FEED_OVERRIDE = G_MAX_FEED_OVERRIDE
    ENDIF
    
    G_FEED_OVERRIDE = FEED_OVERRIDE
    
END

; Validate control flags
DEF VALIDATE_CONTROL_FLAGS(EMERGENCY:IN, CONTROL_MODE:IN, QUALITY:IN)
    DECL BOOL EMERGENCY
    DECL INT CONTROL_MODE, QUALITY
    
    ; Update emergency stop flag
    G_EMERGENCY_STOP = EMERGENCY
    
    ; Validate control mode
    IF (CONTROL_MODE >= 1) AND (CONTROL_MODE <= 3) THEN
        G_CONTROL_MODE = CONTROL_MODE
    ENDIF
    
    ; Update quality flag
    IF (QUALITY >= 0) AND (QUALITY <= 2) THEN
        G_QUALITY_FLAG = QUALITY
    ENDIF
    
END

; Apply low-pass filter to position correction
DEF APPLY_POSITION_FILTER()
    
    DECL REAL FILTER_ALPHA = 0.8  ; Filter coefficient (0-1)
    DECL FRAME FILTERED_POS
    
    ; Apply exponential moving average filter
    FILTERED_POS.X = FILTER_ALPHA * G_POS_CORRECTION.X + (1-FILTER_ALPHA) * G_POS_CORRECTION.X
    FILTERED_POS.Y = FILTER_ALPHA * G_POS_CORRECTION.Y + (1-FILTER_ALPHA) * G_POS_CORRECTION.Y
    FILTERED_POS.Z = FILTER_ALPHA * G_POS_CORRECTION.Z + (1-FILTER_ALPHA) * G_POS_CORRECTION.Z
    
    G_POS_CORRECTION = FILTERED_POS
    
END

; Apply low-pass filter to velocity correction
DEF APPLY_VELOCITY_FILTER()
    
    DECL REAL FILTER_ALPHA = 0.7
    
    ; Apply exponential moving average filter
    G_VEL_CORRECTION = FILTER_ALPHA * G_VEL_CORRECTION + (1-FILTER_ALPHA) * G_VEL_CORRECTION
    
END

; Prepare RSI output data
DEF PREPARE_RSI_OUTPUT(RSI_HANDLE:IN)
    DECL RSI_HANDLE RSI_HANDLE
    
    DECL FRAME CURRENT_POS, TOOL_POS
    DECL REAL CURRENT_VEL, FORCE_DATA
    DECL INT TIMESTAMP
    
    ; Get current robot state
    CURRENT_POS = $POS_ACT
    TOOL_POS = $TOOL
    CURRENT_VEL = $VEL_ACT[1]
    FORCE_DATA = $TORQUE_ACT[6]  ; Z-axis force
    TIMESTAMP = $TIMER[1]
    
    ; Send data to external controller
    RSI_SET_FRAME(RSI_HANDLE, "RobotPos", CURRENT_POS)
    RSI_SET_FRAME(RSI_HANDLE, "ToolPos", TOOL_POS)
    RSI_SET_REAL(RSI_HANDLE, "RobotVel", CURRENT_VEL)
    RSI_SET_REAL(RSI_HANDLE, "ForceData", FORCE_DATA)
    RSI_SET_INT(RSI_HANDLE, "MachiningStatus", G_CONTROL_MODE)
    RSI_SET_INT(RSI_HANDLE, "TimeStamp", TIMESTAMP)
    
END

; Get current correction values
DEF GET_POSITION_CORRECTION() : FRAME
    RETURN G_POS_CORRECTION
END

DEF GET_VELOCITY_CORRECTION() : REAL
    RETURN G_VEL_CORRECTION
END

DEF GET_FEED_OVERRIDE() : REAL
    RETURN G_FEED_OVERRIDE
END

DEF GET_EMERGENCY_STATUS() : BOOL
    RETURN G_EMERGENCY_STOP
END

DEF GET_QUALITY_FLAG() : INT
    RETURN G_QUALITY_FLAG
END

; Set RSI connection status
DEF SET_RSI_CONNECTION(STATUS:IN)
    DECL BOOL STATUS
    G_RSI_CONNECTED = STATUS
END

; Check RSI connection status
DEF GET_RSI_CONNECTION() : BOOL
    RETURN G_RSI_CONNECTED
END
