# KUKA RSI 铣削误差补偿项目

## 项目概述

本项目实现了基于KUKA RSI（Robot Sensor Interface）的实时铣削误差补偿系统。通过实时监测机器人状态和加工力反馈，动态调整机器人位置和速度，以提高铣削加工精度。

## 项目结构

```
kuka RSI/
├── KRL/                          # KUKA机器人程序
│   ├── RSI_Milling_Main.src      # 主控制程序
│   └── RSI_DataHandler.src       # RSI数据处理程序
├── RSI_Config/                   # RSI配置文件
│   └── RSI_Milling.xml           # RSI通讯配置
├── External_Control/             # 外部控制程序
│   ├── RSI_Controller.py         # Python版本控制器
│   └── RSI_Controller.cpp        # C++版本控制器
└── Documentation/                # 项目文档
    ├── README.md                 # 本文件
    ├── Installation_Guide.md     # 安装配置指南
    └── User_Manual.md            # 使用手册
```

## 系统架构

### 1. KUKA机器人端 (KRL程序)
- **RSI_Milling_Main.src**: 主控制程序，负责铣削路径执行和RSI初始化
- **RSI_DataHandler.src**: RSI数据处理程序，处理实时位置补偿和数据验证

### 2. RSI配置 (XML配置文件)
- **RSI_Milling.xml**: 定义通讯参数、数据映射和安全设置

### 3. 外部控制器 (PC端程序)
- **Python版本**: 功能完整，易于扩展和调试
- **C++版本**: 高性能，适合实时性要求高的应用

## 主要功能

### 实时误差补偿
- **力反馈补偿**: 基于切削力监测的位置和进给速度调整
- **位置跟踪补偿**: 预测性位置误差补偿
- **速度自适应控制**: 根据加工条件动态调整进给速度

### 安全保护
- **最大补偿限制**: 防止过大的位置和速度修正
- **紧急停止**: 异常情况下的安全停机
- **数据验证**: 输入数据的有效性检查和滤波

### 通讯协议
- **UDP通讯**: 12ms周期的实时数据交换
- **XML数据格式**: 结构化的数据传输
- **超时保护**: 通讯中断时的安全处理

## 技术参数

| 参数 | 数值 | 说明 |
|------|------|------|
| 通讯周期 | 12ms | RSI实时控制周期 |
| 最大位置补偿 | ±5mm | 单轴最大位置修正量 |
| 最大速度补偿 | ±50% | 速度修正范围 |
| 进给倍率范围 | 0.1-2.0 | 进给速度调整范围 |
| 通讯超时 | 100ms | 网络通讯超时时间 |
| 力阈值 | 50N | 切削力异常检测阈值 |

## 快速开始

### 1. 环境准备
- KUKA机器人控制器 (KRC4或更新版本)
- 支持RSI功能的KUKA系统软件
- PC控制器 (Windows/Linux)
- 以太网连接

### 2. 网络配置
```
机器人IP: *************
PC IP: *************
RSI端口: 49152
子网掩码: *************
```

### 3. 安装步骤
1. 将KRL程序复制到机器人控制器
2. 配置RSI_Milling.xml文件
3. 在PC端安装外部控制程序
4. 配置网络连接
5. 运行系统测试

## 使用流程

### 1. 系统启动
1. 启动KUKA机器人控制器
2. 加载RSI_Milling_Main程序
3. 启动PC端控制程序
4. 验证RSI连接状态

### 2. 铣削作业
1. 设置工件坐标系
2. 配置铣削参数（进给速度、主轴转速）
3. 启动RSI实时控制
4. 执行铣削程序
5. 监控补偿状态

### 3. 系统停止
1. 完成铣削作业
2. 停止RSI控制
3. 关闭外部控制程序
4. 机器人返回安全位置

## 故障排除

### 常见问题
1. **RSI连接失败**
   - 检查网络配置
   - 验证IP地址和端口设置
   - 确认防火墙设置

2. **补偿效果不佳**
   - 调整力阈值参数
   - 检查传感器标定
   - 优化滤波参数

3. **系统响应慢**
   - 检查网络延迟
   - 优化控制算法
   - 减少数据处理负载

## 安全注意事项

⚠️ **重要安全提醒**
- 首次使用前必须进行充分测试
- 设置适当的安全限制参数
- 确保紧急停止功能正常
- 操作人员需要接受专业培训
- 定期检查系统状态和参数

## 技术支持

如需技术支持或有任何问题，请联系：
- 项目维护者：KUKA RSI系统团队
- 创建日期：2025-07-23
- 版本：v1.0

## 许可证

本项目仅供学习和研究使用，商业使用请联系相关方获得授权。
