#!/bin/bash
# KUKA RSI Milling System Startup Script
# 启动脚本 - Linux版本

echo "========================================"
echo "KUKA RSI Milling System Startup"
echo "========================================"
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Error: Python3 is not installed"
    echo "Please install Python 3.7+ and try again"
    exit 1
fi

# Check if numpy is installed
if ! python3 -c "import numpy" &> /dev/null; then
    echo "Installing Python dependencies..."
    pip3 install numpy
    if [ $? -ne 0 ]; then
        echo "Error: Failed to install numpy"
        exit 1
    fi
fi

# Create logs directory if it doesn't exist
mkdir -p logs

# Check network connectivity
echo "Checking network connectivity..."
if ! ping -c 1 ************* &> /dev/null; then
    echo "Warning: Cannot reach robot at *************"
    echo "Please check network configuration"
    echo
fi

# Function to display menu
show_menu() {
    echo "Select startup option:"
    echo "1. Start Python RSI Controller"
    echo "2. Build and start C++ RSI Controller"
    echo "3. Check system status"
    echo "4. View configuration"
    echo "5. Exit"
    echo
}

# Function to start Python controller
start_python() {
    echo
    echo "Starting Python RSI Controller..."
    cd External_Control
    python3 RSI_Controller.py
    cd ..
}

# Function to start C++ controller
start_cpp() {
    echo
    echo "Building C++ RSI Controller..."
    cd External_Control
    
    if [ -f "Makefile" ]; then
        make clean
        make
        if [ $? -eq 0 ]; then
            echo
            echo "Starting C++ RSI Controller..."
            ./RSI_Controller
        else
            echo "Build failed. Please check compiler installation."
        fi
    else
        echo "Makefile not found. Trying direct compilation..."
        g++ -std=c++17 -o RSI_Controller RSI_Controller.cpp -lpthread
        if [ $? -eq 0 ]; then
            echo
            echo "Starting C++ RSI Controller..."
            ./RSI_Controller
        else
            echo "Compilation failed. Please install g++ compiler."
        fi
    fi
    cd ..
}

# Function to check system status
check_status() {
    echo
    echo "System Status Check:"
    echo "=================="
    echo
    echo "Network Configuration:"
    ip addr show | grep "inet " | grep -v "127.0.0.1"
    echo
    echo "Python Version:"
    python3 --version
    echo
    echo "Python Packages:"
    pip3 list | grep numpy
    echo
    echo "File Structure:"
    ls -la KRL RSI_Config External_Control Documentation 2>/dev/null || echo "Some directories not found"
    echo
    read -p "Press Enter to continue..."
}

# Function to view configuration
view_config() {
    echo
    echo "Current Configuration:"
    echo "===================="
    if [ -f "config.ini" ]; then
        cat config.ini
    else
        echo "config.ini not found"
    fi
    echo
    read -p "Press Enter to continue..."
}

# Main menu loop
while true; do
    show_menu
    read -p "Enter your choice (1-5): " choice
    
    case $choice in
        1)
            start_python
            ;;
        2)
            start_cpp
            ;;
        3)
            check_status
            ;;
        4)
            view_config
            ;;
        5)
            echo
            echo "Thank you for using KUKA RSI Milling System"
            echo "System shutdown complete."
            exit 0
            ;;
        *)
            echo "Invalid choice. Please try again."
            ;;
    esac
done
