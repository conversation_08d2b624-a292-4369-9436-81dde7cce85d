/*
KUKA RSI External Controller for Milling Error Compensation (C++ Version)
Real-time position and velocity correction based on sensor feedback

Author: KUKA RSI System
Date: 2025-07-23

Compile with: g++ -std=c++17 -o RSI_Controller RSI_Controller.cpp -lpthread
*/

#include <iostream>
#include <string>
#include <vector>
#include <thread>
#include <chrono>
#include <atomic>
#include <cmath>
#include <sstream>
#include <iomanip>

#ifdef _WIN32
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #pragma comment(lib, "ws2_32.lib")
#else
    #include <sys/socket.h>
    #include <netinet/in.h>
    #include <arpa/inet.h>
    #include <unistd.h>
#endif

class RSIController {
private:
    std::string robot_ip;
    int robot_port;
    int socket_fd;
    std::atomic<bool> running;
    
    // Control parameters
    double max_position_correction = 5.0;  // mm
    double max_velocity_correction = 50.0; // %
    double cycle_time = 0.012;             // 12ms
    
    // Robot state
    struct RobotState {
        double position[6] = {0};
        double velocity = 0.0;
        double force_data = 0.0;
        int machining_status = 0;
        int timestamp = 0;
    } robot_state;
    
    // Correction data
    struct CorrectionData {
        double position_correction[6] = {0};
        double velocity_correction = 0.0;
        double feed_override = 1.0;
        bool emergency_stop = false;
        int control_mode = 1;
        int quality_flag = 0;
    } correction_data;
    
    // Error compensation parameters
    double force_threshold = 50.0;  // N
    std::vector<std::vector<double>> error_history;

public:
    RSIController(const std::string& ip = "*************", int port = 49152)
        : robot_ip(ip), robot_port(port), socket_fd(-1), running(false) {
        
#ifdef _WIN32
        WSADATA wsaData;
        WSAStartup(MAKEWORD(2, 2), &wsaData);
#endif
    }
    
    ~RSIController() {
        stop();
#ifdef _WIN32
        WSACleanup();
#endif
    }
    
    bool connect() {
        // Create UDP socket
        socket_fd = socket(AF_INET, SOCK_DGRAM, 0);
        if (socket_fd < 0) {
            std::cerr << "Failed to create socket" << std::endl;
            return false;
        }
        
        // Set socket timeout
        struct timeval timeout;
        timeout.tv_sec = 0;
        timeout.tv_usec = 100000;  // 100ms
        setsockopt(socket_fd, SOL_SOCKET, SO_RCVTIMEO, (char*)&timeout, sizeof(timeout));
        
        std::cout << "Connected to KUKA robot at " << robot_ip << ":" << robot_port << std::endl;
        return true;
    }
    
    void disconnect() {
        if (socket_fd >= 0) {
#ifdef _WIN32
            closesocket(socket_fd);
#else
            close(socket_fd);
#endif
            socket_fd = -1;
        }
        std::cout << "Disconnected from KUKA robot" << std::endl;
    }
    
    bool parseRobotData(const std::string& data) {
        // Simple XML parsing for robot data
        // In production, use a proper XML parser
        
        try {
            // Extract position data (simplified parsing)
            size_t pos_start = data.find("<RobotPos");
            if (pos_start != std::string::npos) {
                size_t x_pos = data.find("X=\"", pos_start);
                size_t y_pos = data.find("Y=\"", pos_start);
                size_t z_pos = data.find("Z=\"", pos_start);
                
                if (x_pos != std::string::npos && y_pos != std::string::npos && z_pos != std::string::npos) {
                    robot_state.position[0] = std::stod(data.substr(x_pos + 3, data.find("\"", x_pos + 3) - x_pos - 3));
                    robot_state.position[1] = std::stod(data.substr(y_pos + 3, data.find("\"", y_pos + 3) - y_pos - 3));
                    robot_state.position[2] = std::stod(data.substr(z_pos + 3, data.find("\"", z_pos + 3) - z_pos - 3));
                }
            }
            
            // Extract velocity data
            size_t vel_start = data.find("<RobotVel>");
            if (vel_start != std::string::npos) {
                size_t vel_end = data.find("</RobotVel>", vel_start);
                if (vel_end != std::string::npos) {
                    robot_state.velocity = std::stod(data.substr(vel_start + 10, vel_end - vel_start - 10));
                }
            }
            
            // Extract force data
            size_t force_start = data.find("<ForceData>");
            if (force_start != std::string::npos) {
                size_t force_end = data.find("</ForceData>", force_start);
                if (force_end != std::string::npos) {
                    robot_state.force_data = std::stod(data.substr(force_start + 11, force_end - force_start - 11));
                }
            }
            
            return true;
        } catch (const std::exception& e) {
            std::cerr << "Failed to parse robot data: " << e.what() << std::endl;
            return false;
        }
    }
    
    CorrectionData calculateErrorCompensation() {
        CorrectionData correction;
        
        // Initialize correction values
        for (int i = 0; i < 6; i++) {
            correction.position_correction[i] = 0.0;
        }
        correction.velocity_correction = 0.0;
        correction.feed_override = 1.0;
        correction.emergency_stop = false;
        correction.control_mode = 1;
        correction.quality_flag = 0;
        
        // Force-based compensation
        if (std::abs(robot_state.force_data) > force_threshold) {
            double force_error = robot_state.force_data - force_threshold;
            
            // Position correction (move away from high force)
            correction.position_correction[2] = -0.1 * (force_error > 0 ? 1 : -1);  // Z-axis
            
            // Feed rate reduction
            correction.feed_override = std::max(0.5, 1.0 - std::abs(force_error) / 100.0);
            
            // Set quality warning
            correction.quality_flag = 1;
            
            std::cout << "High force detected: " << robot_state.force_data << "N" << std::endl;
        }
        
        // Velocity-based compensation
        if (robot_state.velocity > 0) {
            double velocity_factor = std::min(1.0, 100.0 / robot_state.velocity);
            correction.velocity_correction = (velocity_factor - 1.0) * 10.0;
        }
        
        // Position tracking error compensation
        std::vector<double> current_pos(robot_state.position, robot_state.position + 6);
        error_history.push_back(current_pos);
        
        if (error_history.size() > 10) {
            error_history.erase(error_history.begin());
        }
        
        // Apply limits
        for (int i = 0; i < 6; i++) {
            correction.position_correction[i] = std::max(-max_position_correction,
                std::min(max_position_correction, correction.position_correction[i]));
        }
        
        correction.velocity_correction = std::max(-max_velocity_correction,
            std::min(max_velocity_correction, correction.velocity_correction));
        
        correction.feed_override = std::max(0.1, std::min(2.0, correction.feed_override));
        
        return correction;
    }
    
    std::string createCorrectionXML(const CorrectionData& correction) {
        std::ostringstream xml;
        xml << std::fixed << std::setprecision(3);
        
        xml << "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
        xml << "<RSI>\n";
        xml << "    <PosCorrection X=\"" << correction.position_correction[0] << "\" ";
        xml << "Y=\"" << correction.position_correction[1] << "\" ";
        xml << "Z=\"" << correction.position_correction[2] << "\" ";
        xml << "A=\"" << correction.position_correction[3] << "\" ";
        xml << "B=\"" << correction.position_correction[4] << "\" ";
        xml << "C=\"" << correction.position_correction[5] << "\" />\n";
        xml << "    <VelCorrection>" << correction.velocity_correction << "</VelCorrection>\n";
        xml << "    <FeedOverride>" << correction.feed_override << "</FeedOverride>\n";
        xml << "    <EmergencyStop>" << (correction.emergency_stop ? "true" : "false") << "</EmergencyStop>\n";
        xml << "    <ControlMode>" << correction.control_mode << "</ControlMode>\n";
        xml << "    <QualityFlag>" << correction.quality_flag << "</QualityFlag>\n";
        xml << "</RSI>";
        
        return xml.str();
    }
    
    void runControlLoop() {
        std::cout << "Starting RSI control loop" << std::endl;
        running = true;
        
        struct sockaddr_in robot_addr;
        robot_addr.sin_family = AF_INET;
        robot_addr.sin_port = htons(robot_port);
        inet_pton(AF_INET, robot_ip.c_str(), &robot_addr.sin_addr);
        
        char buffer[1024];
        socklen_t addr_len = sizeof(robot_addr);
        
        while (running) {
            // Receive data from robot
            int bytes_received = recvfrom(socket_fd, buffer, sizeof(buffer) - 1, 0,
                                        (struct sockaddr*)&robot_addr, &addr_len);
            
            if (bytes_received > 0) {
                buffer[bytes_received] = '\0';
                std::string data(buffer);
                
                // Parse robot state
                if (parseRobotData(data)) {
                    // Calculate error compensation
                    CorrectionData correction = calculateErrorCompensation();
                    
                    // Create response XML
                    std::string response_xml = createCorrectionXML(correction);
                    
                    // Send correction back to robot
                    sendto(socket_fd, response_xml.c_str(), response_xml.length(), 0,
                          (struct sockaddr*)&robot_addr, addr_len);
                    
                    // Log status
                    if (correction.quality_flag > 0) {
                        std::cout << "Correction applied: Pos=[" 
                                 << correction.position_correction[0] << ", "
                                 << correction.position_correction[1] << ", "
                                 << correction.position_correction[2] << "], "
                                 << "Feed=" << correction.feed_override << std::endl;
                    }
                }
            }
            
            // Maintain cycle time
            std::this_thread::sleep_for(std::chrono::milliseconds(static_cast<int>(cycle_time * 1000)));
        }
        
        std::cout << "RSI control loop stopped" << std::endl;
    }
    
    bool start() {
        if (!connect()) {
            return false;
        }
        
        // Start control loop in separate thread
        std::thread control_thread(&RSIController::runControlLoop, this);
        control_thread.detach();
        
        return true;
    }
    
    void stop() {
        running = false;
        disconnect();
    }
};

int main() {
    RSIController controller;
    
    try {
        if (controller.start()) {
            std::cout << "RSI Controller started successfully" << std::endl;
            std::cout << "Press Enter to stop..." << std::endl;
            
            // Wait for user input
            std::cin.get();
        }
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
    }
    
    controller.stop();
    std::cout << "RSI Controller stopped" << std::endl;
    
    return 0;
}
